"use client";

import React, { useState, useEffect, useMemo, useC<PERSON>back, memo, Suspense } from "react";
import { APIProvider, Map, Marker, useMap } from "@vis.gl/react-google-maps";
import { MapPin, Navigation, AlertCircle, Loader2, Clock } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils/tailwind";
import { createLogger } from "@/lib/utils/logger/logger";
import { useIsMobile } from "@/lib/hooks/use-mobile";

// Import our route visualization service
import {
  calculateRouteVisualization,
} from "@/lib/services/google-maps/route-visualization.service";
import {
  calculateOptimalBounds,
  DEFAULT_ROUTE_STYLE,
} from "@/lib/services/google-maps/utils";

import type {
  RouteCoordinates,
  RouteVisualizationResult,
  RouteBounds,
} from "@/lib/services/google-maps/types";

const logger = createLogger("RouteMap");

// Get Google Maps API key from environment
const GOOGLE_MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!;

if (!GOOGLE_MAPS_API_KEY) {
  throw new Error("NEXT_PUBLIC_GOOGLE_MAPS_API_KEY is not defined");
}

/**
 * Cache validation utilities
 */
const isCacheStale = (timestamp: string | null, thresholdHours: number): boolean => {
  if (!timestamp) return true;
  const cacheTime = new Date(timestamp).getTime();
  const now = Date.now();
  const thresholdMs = thresholdHours * 60 * 60 * 1000;
  return (now - cacheTime) > thresholdMs;
};

const shouldForceRefresh = (timestamp: string | null, thresholdDays: number): boolean => {
  if (!timestamp) return true;
  const cacheTime = new Date(timestamp).getTime();
  const now = Date.now();
  const thresholdMs = thresholdDays * 24 * 60 * 60 * 1000;
  return (now - cacheTime) > thresholdMs;
};

// Component state types
type LoadingState = "idle" | "loading-distance" | "loading-map" | "loaded" | "error";

// Cache validation constants
const CACHE_STALE_THRESHOLD_HOURS = 24; // Consider cache stale after 24 hours
const CACHE_REFRESH_THRESHOLD_DAYS = 7; // Force refresh after 7 days

interface RouteMapProps {
  rfqId: string;
  // Cached coordinate data from RFQ
  originLat?: number | null;
  originLng?: number | null;
  destinationLat?: number | null;
  destinationLng?: number | null;
  routeDistanceKm?: number | null;
  // Cache metadata
  coordinatesResolvedAt?: string | null;
  routeDistanceCalculatedAt?: string | null;
  // Address fallback data
  originCity?: string;
  originAddress?: string;
  originPostalCode?: string;
  originCountryName?: string;
  destinationCity?: string;
  destinationAddress?: string;
  destinationPostalCode?: string;
  destinationCountryName?: string;
  className?: string;
}

/**
 * Route Map Component
 *
 * Displays an interactive Google Maps route visualization for RFQ logistics planning.
 * Features:
 * - Cached distance display (instant loading)
 * - Interactive route visualization with polyline
 * - Origin/destination markers with custom icons
 * - Progressive loading states
 * - Comprehensive error handling and fallbacks
 * - Responsive design
 */
export function RouteMap({
  rfqId,
  originLat,
  originLng,
  destinationLat,
  destinationLng,
  routeDistanceKm,
  coordinatesResolvedAt,
  routeDistanceCalculatedAt,
  originCity,
  originAddress,
  originPostalCode,
  originCountryName,
  destinationCity,
  destinationAddress,
  destinationPostalCode,
  destinationCountryName,
  className,
}: RouteMapProps) {
  // Responsive design hook
  const isMobile = useIsMobile();

  // Component state
  const [loadingState, setLoadingState] = useState<LoadingState>("idle");
  const [routeVisualization, setRouteVisualization] = useState<RouteVisualizationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Cache validation - memoized for performance
  const cacheStatus = useMemo(() => {
    const coordinatesStale = isCacheStale(coordinatesResolvedAt || null, CACHE_STALE_THRESHOLD_HOURS);
    const distanceStale = isCacheStale(routeDistanceCalculatedAt || null, CACHE_STALE_THRESHOLD_HOURS);
    const shouldRefreshCoordinates = shouldForceRefresh(coordinatesResolvedAt || null, CACHE_REFRESH_THRESHOLD_DAYS);
    const shouldRefreshDistance = shouldForceRefresh(routeDistanceCalculatedAt || null, CACHE_REFRESH_THRESHOLD_DAYS);

    return {
      coordinatesStale,
      distanceStale,
      shouldRefreshCoordinates,
      shouldRefreshDistance,
      isStale: coordinatesStale || distanceStale,
      needsRefresh: shouldRefreshCoordinates || shouldRefreshDistance,
    };
  }, [coordinatesResolvedAt, routeDistanceCalculatedAt]);

  // Check if we have valid coordinates - memoized for performance
  const hasValidCoordinates = useMemo(() => {
    return (
      typeof originLat === "number" &&
      typeof originLng === "number" &&
      typeof destinationLat === "number" &&
      typeof destinationLng === "number" &&
      !isNaN(originLat) &&
      !isNaN(originLng) &&
      !isNaN(destinationLat) &&
      !isNaN(destinationLng)
    );
  }, [originLat, originLng, destinationLat, destinationLng]);

  // Create route coordinates object
  const routeCoordinates: RouteCoordinates | null = useMemo(() => {
    if (!hasValidCoordinates) return null;

    return {
      origin: {
        lat: originLat!,
        lng: originLng!,
      },
      destination: {
        lat: destinationLat!,
        lng: destinationLng!,
      },
    };
  }, [hasValidCoordinates, originLat, originLng, destinationLat, destinationLng]);

  // Note: Custom markers are handled directly in the RouteMapDisplay component

  // Calculate map bounds
  const mapBounds: RouteBounds | null = useMemo(() => {
    if (!routeCoordinates) return null;
    return calculateOptimalBounds(routeCoordinates);
  }, [routeCoordinates]);

  // Format cached distance for display
  const formattedDistance = useMemo(() => {
    if (typeof routeDistanceKm === "number" && !isNaN(routeDistanceKm)) {
      return `${routeDistanceKm.toFixed(1)} km`;
    }
    return null;
  }, [routeDistanceKm]);

  // Format address strings for fallback display
  const originAddressString = useMemo(() => {
    const parts = [originAddress, originCity, originPostalCode, originCountryName].filter(Boolean);
    return parts.length > 0 ? parts.join(", ") : "Origin address not available";
  }, [originAddress, originCity, originPostalCode, originCountryName]);

  const destinationAddressString = useMemo(() => {
    const parts = [destinationAddress, destinationCity, destinationPostalCode, destinationCountryName].filter(Boolean);
    return parts.length > 0 ? parts.join(", ") : "Destination address not available";
  }, [destinationAddress, destinationCity, destinationPostalCode, destinationCountryName]);

  // Memoized route visualization loader for performance
  const loadRouteVisualization = useCallback(async () => {
    if (!hasValidCoordinates || !routeCoordinates) {
      setLoadingState("error");
      setError("Coordinates not available for route visualization");
      return;
    }

    try {
      setLoadingState("loading-map");
      setError(null);

      logger.info("Loading route visualization", {
        rfqId,
        routeCoordinates,
        cacheStatus: cacheStatus.isStale ? "stale" : "fresh"
      });

      const result = await calculateRouteVisualization(routeCoordinates, {
        travelMode: "DRIVE",
        units: "METRIC",
        language: "en",
      });

      if (result.success) {
        setRouteVisualization(result);
        setLoadingState("loaded");
        logger.info("Route visualization loaded successfully", { rfqId });
      } else {
        throw new Error(result.error || "Failed to calculate route visualization");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
      logger.error("Error loading route visualization:", { rfqId, error: errorMessage });
      setError(errorMessage);
      setLoadingState("error");
    }
  }, [hasValidCoordinates, routeCoordinates, rfqId, cacheStatus.isStale]);

  // Load route visualization when coordinates are available
  useEffect(() => {
    loadRouteVisualization();
  }, [loadRouteVisualization]);

  // Initialize loading state based on available data
  useEffect(() => {
    if (formattedDistance) {
      setLoadingState("loading-distance");
    } else {
      setLoadingState("idle");
    }
  }, [formattedDistance]);

  return (
    <div className={cn(
      "space-y-6",
      // Responsive spacing and layout
      isMobile ? "space-y-4" : "space-y-6",
      className
    )}>
      {/* Distance Information Card - Always shows first */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Navigation className="h-5 w-5 text-blue-600" />
            Route Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Distance Display */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">Distance:</span>
            {loadingState === "idle" || loadingState === "loading-distance" ? (
              <Skeleton className="h-6 w-20" />
            ) : formattedDistance ? (
              <Badge variant="secondary" className="text-sm font-semibold">
                {formattedDistance}
              </Badge>
            ) : (
              <span className="text-sm text-muted-foreground">Not calculated</span>
            )}
          </div>

          {/* Estimated Duration Display */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">Estimated Duration:</span>
            {loadingState === "loading-map" ? (
              <Skeleton className="h-6 w-20" />
            ) : loadingState === "loaded" && routeVisualization?.durationText ? (
              <Badge variant="outline" className="text-sm">
                <Clock className="h-3 w-3 mr-1" />
                {routeVisualization.durationText}
              </Badge>
            ) : (
              <span className="text-sm text-muted-foreground">Not available</span>
            )}
          </div>

          {/* Route Status */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">Route Status:</span>
            {loadingState === "loading-map" ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm text-muted-foreground">Loading map...</span>
              </div>
            ) : loadingState === "loaded" ? (
              <Badge variant="default" className="text-sm">
                Ready
              </Badge>
            ) : loadingState === "error" ? (
              <Badge variant="destructive" className="text-sm">
                Error
              </Badge>
            ) : (
              <Badge variant="outline" className="text-sm">
                Preparing...
              </Badge>
            )}
          </div>

          {/* Cache Status - Performance indicator */}
          {(cacheStatus.isStale || cacheStatus.needsRefresh) && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-muted-foreground">Cache Status:</span>
              <Badge
                variant={cacheStatus.needsRefresh ? "destructive" : "secondary"}
                className="text-xs"
              >
                {cacheStatus.needsRefresh ? "Needs Refresh" : "Stale"}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Map Visualization Card */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <MapPin className="h-5 w-5 text-blue-600" />
            Route Map
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Error State */}
          {loadingState === "error" && (
            <Alert className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error || "Unable to load route map. Please check if coordinates are available."}
                {!hasValidCoordinates && (
                  <div className="mt-2 text-sm">
                    <p>Showing address information instead:</p>
                    <div className="mt-2 space-y-1">
                      <p><strong>Origin:</strong> {originAddressString}</p>
                      <p><strong>Destination:</strong> {destinationAddressString}</p>
                    </div>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* Map Container - Responsive Height */}
          <div className={cn(
            "relative w-full rounded-lg overflow-hidden border bg-muted",
            // Responsive height based on device type
            isMobile ? "h-64" : "h-96 lg:h-[500px]"
          )}>
            {/* Loading State */}
            {(loadingState === "idle" || loadingState === "loading-distance" || loadingState === "loading-map") && (
              <div className="absolute inset-0 flex items-center justify-center bg-muted">
                <div className="text-center space-y-3">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto text-muted-foreground" />
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-32 mx-auto" />
                    <Skeleton className="h-3 w-24 mx-auto" />
                  </div>
                </div>
              </div>
            )}

            {/* Map Display */}
            {loadingState === "loaded" && hasValidCoordinates && routeCoordinates && (
              <APIProvider apiKey={GOOGLE_MAPS_API_KEY}>
                <RouteMapDisplay
                  routeCoordinates={routeCoordinates}
                  routeVisualization={routeVisualization}
                  originCity={originCity}
                  destinationCity={destinationCity}
                  mapBounds={mapBounds}
                />
              </APIProvider>
            )}

            {/* Error State - No Coordinates */}
            {loadingState === "error" && !hasValidCoordinates && (
              <div className="absolute inset-0 flex items-center justify-center bg-muted">
                <div className="text-center space-y-3 p-6">
                  <AlertCircle className="h-12 w-12 mx-auto text-muted-foreground" />
                  <div className="space-y-2">
                    <h3 className="font-medium text-foreground">Map Not Available</h3>
                    <p className="text-sm text-muted-foreground max-w-sm">
                      Coordinates are not available for this route. The map will be displayed once the addresses are geocoded.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Separate map display component for better organization - memoized for performance
interface RouteMapDisplayProps {
  routeCoordinates: RouteCoordinates;
  routeVisualization: RouteVisualizationResult | null;
  originCity?: string;
  destinationCity?: string;
  mapBounds: RouteBounds | null;
}

const RouteMapDisplay = memo(function RouteMapDisplay({
  routeCoordinates,
  routeVisualization,
  originCity,
  destinationCity,
  mapBounds,
}: RouteMapDisplayProps) {
  // Calculate center point between origin and destination
  const mapCenter = useMemo(() => {
    return {
      lat: (routeCoordinates.origin.lat + routeCoordinates.destination.lat) / 2,
      lng: (routeCoordinates.origin.lng + routeCoordinates.destination.lng) / 2,
    };
  }, [routeCoordinates]);

  // Calculate appropriate zoom level based on distance
  const mapZoom = useMemo(() => {
    if (!mapBounds) return 10;

    const latDiff = Math.abs(mapBounds.northeast.lat - mapBounds.southwest.lat);
    const lngDiff = Math.abs(mapBounds.northeast.lng - mapBounds.southwest.lng);
    const maxDiff = Math.max(latDiff, lngDiff);

    // Simple zoom calculation based on coordinate difference
    if (maxDiff > 10) return 5;
    if (maxDiff > 5) return 6;
    if (maxDiff > 2) return 7;
    if (maxDiff > 1) return 8;
    if (maxDiff > 0.5) return 9;
    if (maxDiff > 0.1) return 10;
    return 11;
  }, [mapBounds]);

  return (
    <Map
      style={{ width: "100%", height: "100%" }}
      defaultCenter={mapCenter}
      defaultZoom={mapZoom}
      gestureHandling="greedy"
      disableDefaultUI={true}
      mapTypeControl={false}
      streetViewControl={false}
      fullscreenControl={false}
      zoomControl={true}
      mapId="route-map"
      // Responsive zoom constraints
      minZoom={3}
      maxZoom={18}
    >
      {/* Origin Marker with custom styling */}
      <Marker
        position={routeCoordinates.origin}
        title={`Origin: ${originCity || "Unknown"}`}
      />

      {/* Destination Marker with custom styling */}
      <Marker
        position={routeCoordinates.destination}
        title={`Destination: ${destinationCity || "Unknown"}`}
      />

      {/* Route Polyline */}
      {routeVisualization?.polyline && (
        <RoutePolyline
          encodedPath={routeVisualization.polyline}
          strokeColor={DEFAULT_ROUTE_STYLE.strokeColor}
          strokeWeight={DEFAULT_ROUTE_STYLE.strokeWeight}
          strokeOpacity={DEFAULT_ROUTE_STYLE.strokeOpacity}
        />
      )}
    </Map>
  );
});

/**
 * Decodes a Google Maps encoded polyline string into an array of lat/lng coordinates
 * @param encoded The encoded polyline string
 * @returns Array of {lat, lng} coordinates
 */
function decodePolyline(encoded: string): Array<{ lat: number; lng: number }> {
  const poly: Array<{ lat: number; lng: number }> = [];
  let index = 0;
  const len = encoded.length;
  let lat = 0;
  let lng = 0;

  while (index < len) {
    let b: number;
    let shift = 0;
    let result = 0;

    do {
      b = encoded.charCodeAt(index++) - 63;
      result |= (b & 0x1f) << shift;
      shift += 5;
    } while (b >= 0x20);

    const dlat = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
    lat += dlat;

    shift = 0;
    result = 0;

    do {
      b = encoded.charCodeAt(index++) - 63;
      result |= (b & 0x1f) << shift;
      shift += 5;
    } while (b >= 0x20);

    const dlng = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
    lng += dlng;

    poly.push({ lat: lat / 1e5, lng: lng / 1e5 });
  }

  return poly;
}

// Helper component for route polyline
interface RoutePolylineProps {
  encodedPath: string;
  strokeColor?: string;
  strokeWeight?: number;
  strokeOpacity?: number;
}

function RoutePolyline({
  encodedPath,
  strokeColor = "#2563eb",
  strokeWeight = 4,
  strokeOpacity = 0.8
}: RoutePolylineProps) {
  const map = useMap();
  const [polyline, setPolyline] = useState<google.maps.Polyline | null>(null);

  // Decode the polyline path
  const decodedPath = useMemo(() => {
    try {
      return decodePolyline(encodedPath);
    } catch (error) {
      logger.error("Error decoding polyline:", error);
      return [];
    }
  }, [encodedPath]);

  useEffect(() => {
    if (!map || decodedPath.length === 0) {
      return;
    }

    // Create the polyline
    const newPolyline = new google.maps.Polyline({
      path: decodedPath,
      strokeColor,
      strokeWeight,
      strokeOpacity,
    });

    // Add to map
    newPolyline.setMap(map);
    setPolyline(newPolyline);

    // Cleanup function
    return () => {
      if (newPolyline) {
        newPolyline.setMap(null);
      }
    };
  }, [map, decodedPath, strokeColor, strokeWeight, strokeOpacity]);

  // Update polyline properties when they change
  useEffect(() => {
    if (polyline) {
      polyline.setOptions({
        strokeColor,
        strokeWeight,
        strokeOpacity,
      });
    }
  }, [polyline, strokeColor, strokeWeight, strokeOpacity]);

  // This component doesn't render anything to the DOM
  return null;
}

/**
 * Loading skeleton for the route map component
 */
export function RouteMapSkeleton() {
  return (
    <div className="space-y-6">
      {/* Route Information Card Skeleton */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Navigation className="h-5 w-5 text-blue-600" />
            Route Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Distance Display Skeleton */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">Distance:</span>
            <Skeleton className="h-6 w-20" />
          </div>

          {/* Estimated Duration Skeleton */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">Estimated Duration:</span>
            <Skeleton className="h-6 w-20" />
          </div>

          {/* Route Status Skeleton */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">Route Status:</span>
            <Skeleton className="h-6 w-16" />
          </div>

          {/* Address Information Skeleton */}
          <div className="space-y-2 pt-2 border-t">
            <div className="flex items-start gap-2">
              <MapPin className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium">Origin</p>
                <Skeleton className="h-4 w-full max-w-xs" />
              </div>
            </div>
            <div className="flex items-start gap-2">
              <MapPin className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium">Destination</p>
                <Skeleton className="h-4 w-full max-w-xs" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Map Visualization Card Skeleton */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <MapPin className="h-5 w-5 text-blue-600" />
            Route Map
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Map Container Skeleton */}
          <div className="relative w-full h-96 rounded-lg overflow-hidden border bg-muted">
            <div className="absolute inset-0 flex items-center justify-center bg-muted">
              <div className="text-center space-y-3">
                <Loader2 className="h-8 w-8 animate-spin mx-auto text-muted-foreground" />
                <div className="space-y-1">
                  <Skeleton className="h-4 w-32 mx-auto" />
                  <Skeleton className="h-3 w-24 mx-auto" />
                </div>
                <p className="text-sm text-muted-foreground">Loading map components...</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Lazy-loaded Route Map Component
 *
 * This component implements lazy loading for the RouteMap component to improve
 * initial page load performance. The map components are only loaded when needed.
 */
export function LazyRouteMap(props: RouteMapProps) {
  return (
    <Suspense fallback={<RouteMapSkeleton />}>
      <RouteMap {...props} />
    </Suspense>
  );
}
